// Settings page functionality
let qrCodeInstance = null;

// Load 2FA status
async function load2FAStatus() {
    try {
        showLoading('load-2fa');
        const result = await apiManager.get2FAStatus();
        
        const statusText = document.getElementById('2fa-status-text');
        const setupBtn = document.getElementById('setup-2fa-btn');
        const disableBtn = document.getElementById('disable-2fa-btn');
        const backupCodesBtn = document.getElementById('generate-backup-codes-btn');
        
        if (result.enabled) {
            statusText.innerHTML = '<span class="status-indicator status-enabled">Enabled</span>';
            setupBtn.style.display = 'none';
            disableBtn.style.display = 'inline-block';
            backupCodesBtn.style.display = 'inline-block';
        } else {
            statusText.innerHTML = '<span class="status-indicator status-disabled">Disabled</span>';
            setupBtn.style.display = 'inline-block';
            disableBtn.style.display = 'none';
            backupCodesBtn.style.display = 'none';
        }
        
        hideLoading('load-2fa');
    } catch (error) {
        hideLoading('load-2fa');
        showStatus('settings-status', 'Error loading 2FA status: ' + error.message, 'error');
    }
}

// Setup 2FA
async function setup2FA() {
    try {
        showLoading('load-2fa');
        const result = await apiManager.setup2FA();
        
        document.getElementById('2fa-secret').textContent = result.secret;
        document.getElementById('2fa-setup').style.display = 'block';
        document.getElementById('2fa-controls').style.display = 'none';
        
        // Generate QR code
        generateQRCode(result.qrCodeUrl);
        
        hideLoading('load-2fa');
    } catch (error) {
        hideLoading('load-2fa');
        showStatus('settings-status', 'Error setting up 2FA: ' + error.message, 'error');
    }
}

// Generate QR code (simplified version)
function generateQRCode(url) {
    const qrContainer = document.getElementById('qr-code');
    qrContainer.innerHTML = `
        <div style="border: 2px solid #ccc; padding: 20px; background: white; display: inline-block;">
            <p>QR Code would appear here</p>
            <p style="font-size: 12px; color: #666;">Manual setup key: ${document.getElementById('2fa-secret').textContent}</p>
        </div>
    `;
}

// Verify 2FA setup
async function verify2FASetup() {
    const code = document.getElementById('2fa-verify-code').value.trim();
    
    if (!code || code.length !== 6) {
        showStatus('settings-status', 'Please enter a valid 6-digit code', 'error');
        return;
    }
    
    try {
        showLoading('load-2fa');
        const result = await apiManager.verify2FASetup(code);
        
        if (result.success) {
            showStatus('settings-status', '2FA enabled successfully!', 'success');
            
            // Show backup codes
            if (result.backupCodes) {
                displayBackupCodes(result.backupCodes);
            }
            
            // Reset UI
            cancel2FASetup();
            load2FAStatus();
        } else {
            showStatus('settings-status', 'Invalid verification code', 'error');
        }
        
        hideLoading('load-2fa');
    } catch (error) {
        hideLoading('load-2fa');
        showStatus('settings-status', 'Error verifying 2FA: ' + error.message, 'error');
    }
}

// Cancel 2FA setup
function cancel2FASetup() {
    document.getElementById('2fa-setup').style.display = 'none';
    document.getElementById('2fa-controls').style.display = 'block';
    document.getElementById('2fa-verify-code').value = '';
}

// Disable 2FA
async function disable2FA() {
    if (!confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
        return;
    }
    
    try {
        showLoading('load-2fa');
        await apiManager.disable2FA();
        
        showStatus('settings-status', '2FA disabled successfully', 'success');
        load2FAStatus();
        hideLoading('load-2fa');
    } catch (error) {
        hideLoading('load-2fa');
        showStatus('settings-status', 'Error disabling 2FA: ' + error.message, 'error');
    }
}

// Generate backup codes
async function generateBackupCodes() {
    try {
        showLoading('load-2fa');
        const result = await apiManager.generateBackupCodes();
        
        if (result.backupCodes) {
            displayBackupCodes(result.backupCodes);
        }
        
        hideLoading('load-2fa');
    } catch (error) {
        hideLoading('load-2fa');
        showStatus('settings-status', 'Error generating backup codes: ' + error.message, 'error');
    }
}

// Display backup codes
function displayBackupCodes(codes) {
    const codesList = document.getElementById('backup-codes-list');
    codesList.innerHTML = codes.map(code => 
        `<span class="backup-code">${code}</span>`
    ).join('');
    
    document.getElementById('backup-codes-display').style.display = 'block';
}

// Hide backup codes
function hideBackupCodes() {
    document.getElementById('backup-codes-display').style.display = 'none';
}

// Change password
async function changePassword() {
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-new-password').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showStatus('settings-status', 'Please fill in all password fields', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showStatus('settings-status', 'New passwords do not match', 'error');
        return;
    }
    
    if (newPassword.length < 8) {
        showStatus('settings-status', 'New password must be at least 8 characters long', 'error');
        return;
    }
    
    try {
        showLoading('load-settings');
        
        // Verify current password by trying to decrypt a test note
        const testResult = await apiManager.verifyPassword(currentPassword);
        if (!testResult.valid) {
            showStatus('settings-status', 'Current password is incorrect', 'error');
            hideLoading('load-settings');
            return;
        }
        
        // Change password
        await apiManager.changePassword(currentPassword, newPassword);
        
        showStatus('settings-status', 'Password changed successfully! Please log in again.', 'success');
        
        // Clear form
        document.getElementById('current-password').value = '';
        document.getElementById('new-password').value = '';
        document.getElementById('confirm-new-password').value = '';
        
        // Logout after password change
        setTimeout(() => {
            logout();
        }, 2000);
        
        hideLoading('load-settings');
    } catch (error) {
        hideLoading('load-settings');
        showStatus('settings-status', 'Error changing password: ' + error.message, 'error');
    }
}

// Load account information
async function loadAccountInfo() {
    try {
        const result = await apiManager.getAccountInfo();
        
        document.getElementById('user-email').textContent = result.email || 'N/A';
        document.getElementById('account-created').textContent = result.createdAt ? 
            new Date(result.createdAt).toLocaleDateString() : 'N/A';
        document.getElementById('last-login').textContent = result.lastLogin ? 
            new Date(result.lastLogin).toLocaleDateString() : 'N/A';
            
    } catch (error) {
        showStatus('settings-status', 'Error loading account info: ' + error.message, 'error');
    }
}

// Logout function
function logout() {
    // Clear sensitive data
    if (encryptionKey) {
        CryptoUtils.clearSensitiveData(encryptionKey);
    }
    
    // Clear session
    currentUser = null;
    encryptionKey = null;
    
    if (apiManager) {
        apiManager.clearToken();
    }
    
    // Redirect to login
    window.location.href = 'index.html';
}
