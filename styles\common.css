/* Common styles for SecureNotes application */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 800px;
    backdrop-filter: blur(10px);
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #333;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.header p {
    color: #666;
    font-size: 1.1em;
}

.crypto-info {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    border-left: 4px solid #28a745;
}

.crypto-info h3 {
    color: #28a745;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.crypto-info p {
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.95em;
    line-height: 1.5;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s, box-shadow 0.3s;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    line-height: 1.5;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.btn-small {
    padding: 6px 12px;
    font-size: 14px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.status {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    display: none;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.char-counter {
    font-size: 12px;
    color: #6c757d;
    text-align: right;
    margin-top: 5px;
}

.char-counter.warning {
    color: #856404;
}

.char-counter.error {
    color: #721c24;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.loading.show {
    display: block;
}

.hidden {
    display: none !important;
}

/* Navigation styles */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid #e1e5e9;
    margin-bottom: 20px;
}

.nav-tab {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 16px;
    color: #6c757d;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.nav-tab:hover {
    color: #667eea;
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
        border-radius: 15px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tab {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .header h1 {
        font-size: 1.8em;
    }
    
    .crypto-info {
        padding: 15px;
    }
}
