/**
 * Notes management module for SecureNotes application
 */

// Global state for notes
let allNotes = [];
let decryptedNotes = new Map();
let editingNoteId = null;
let currentPage = 1;
let totalPages = 1;

// Load and display notes
async function loadNotes(page = 1) {
    if (!currentUser || !encryptionKey) {
        return;
    }

    currentPage = page;
    LoadingManager.showLoading('load-notes');

    try {
        showStatus('notes-status', 'Loading notes...', 'info');
        allNotes = await apiManager.getNotes();
        const notesListEl = document.getElementById('notes-list');

        if (allNotes.length === 0) {
            notesListEl.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">No notes yet, write your first one!</p>';
            document.getElementById('pagination-controls').style.display = 'none';
            showStatus('notes-status', '', 'info');
            return;
        }

        // Calculate pagination
        const notesPerPage = 10;
        totalPages = Math.ceil(allNotes.length / notesPerPage);
        const startIndex = (currentPage - 1) * notesPerPage;
        const endIndex = startIndex + notesPerPage;
        const currentPageNotes = allNotes.slice(startIndex, endIndex);

        let notesHtml = `<h4>My Notes (${allNotes.length} items)</h4>`;

        for (const note of currentPageNotes) {
            try {
                const createdAt = new Date(note.created_at).toLocaleString('zh-CN');
                let displayContent = '';
                let hasExpandBtn = false;

                // Check if note is currently being edited
                if (editingNoteId == note.id) {
                    // Check if editing interface actually exists
                    const editingElement = document.getElementById(`edit-textarea-${note.id}`);
                    if (editingElement) {
                        // Skip rendering for editing note, it will be handled by editNote function
                        continue;
                    } else {
                        // Editing interface doesn't exist, clear the editing state
                        editingNoteId = null;
                    }
                }

                // Try to decrypt preview title first
                if (note.encrypted_preview_title) {
                    try {
                        const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                        displayContent = escapeHTML(previewTitle) + '...';
                        hasExpandBtn = true;
                    } catch (error) {
                        displayContent = 'Unable to decrypt preview';
                    }
                } else {
                    // Fallback for old notes without preview title
                    displayContent = 'Click to expand...';
                    hasExpandBtn = true;
                }

                notesHtml += `
                    <div class="note-item" data-note-id="${note.id}">
                        <div class="note-actions">
                            <button class="btn btn-small edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                            <button class="btn btn-danger btn-small delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
                        </div>
                        <div class="note-meta">Created: ${createdAt}</div>
                        <div class="note-content collapsed">${displayContent}</div>
                        ${hasExpandBtn ? '<button class="note-expand-btn" onclick="expandNote(' + note.id + ')">展开</button>' : ''}
                    </div>
                `;
            } catch (error) {
                console.error('Failed to process note:', error);
                notesHtml += `
                    <div class="note-item" style="border-left-color: #dc3545;">
                        <div class="note-meta">Failed to process note</div>
                        <div class="note-content" style="color: #dc3545;">Unable to process this note</div>
                    </div>
                `;
            }
        }

        notesListEl.innerHTML = notesHtml;
        updatePaginationControls();
        showStatus('notes-status', '', 'info');
    } catch (error) {
        console.error('Load notes error:', error);
        showStatus('notes-status', 'Failed to load notes', 'error');
    } finally {
        LoadingManager.hideLoading('load-notes');
    }
}

// Save a new note
async function saveNote() {
    const content = document.getElementById('note-content').value.trim();

    if (!validateInput(content, 'note')) {
        if (!content) {
            showStatus('notes-status', 'Note content cannot be empty', 'error');
        } else if (content.length > 1000) {
            showStatus('notes-status', 'Note content cannot exceed 1000 characters', 'error');
        } else {
            showStatus('notes-status', 'Note content must be at least 1 character', 'error');
        }
        return;
    }

    if (content.length > CONFIG.MAX_NOTE_LENGTH) {
        showStatus('notes-status', `Note content cannot exceed ${CONFIG.MAX_NOTE_LENGTH} characters`, 'error');
        return;
    }

    try {
        LoadingManager.showLoading('load-save');
        showStatus('notes-status', 'Saving note...', 'info');

        // Generate preview title (1/3 of content, max 15 chars)
        const previewLength = Math.min(Math.floor(content.length / 3), 15);
        const previewTitle = content.substring(0, previewLength);

        const encryptedContent = await CryptoUtils.encryptText(content, encryptionKey);
        const encryptedPreviewTitle = await CryptoUtils.encryptText(previewTitle, encryptionKey);
        const result = await apiManager.saveNote(encryptedContent, encryptedPreviewTitle);

        if (result.success) {
            document.getElementById('note-content').value = '';
            showStatus('notes-status', 'Note saved successfully!', 'success');
            loadNotes();
        } else {
            showStatus('notes-status', result.error || 'Save failed, please try again', 'error');
        }
    } catch (error) {
        console.error('Save note error:', error);
        showStatus('notes-status', 'Failed to save note', 'error');
    } finally {
        LoadingManager.hideLoading('load-save');
    }
}

// Edit note
async function editNote(noteId) {
    if (editingNoteId) {
        showStatus('notes-status', 'Please finish editing the current note first', 'error');
        return;
    }

    const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
    if (!noteElement) return;

    // Decrypt full content if not already decrypted
    let fullContent = decryptedNotes.get(noteId);
    if (!fullContent) {
        const note = allNotes.find(n => n.id == noteId);
        if (note) {
            try {
                fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                decryptedNotes.set(noteId, fullContent);
            } catch (error) {
                showStatus('notes-status', 'Failed to decrypt note', 'error');
                return;
            }
        }
    }

    // Replace note content with editable textarea
    const contentElement = noteElement.querySelector('.note-content');
    const actionsElement = noteElement.querySelector('.note-actions');
    
    contentElement.innerHTML = `
        <textarea id="edit-textarea-${noteId}" class="edit-textarea">${escapeHTML(fullContent)}</textarea>
        <div class="char-counter" id="edit-char-counter-${noteId}">0/1000</div>
    `;

    actionsElement.innerHTML = `
        <button class="btn btn-small" id="save-edit-${noteId}">Save</button>
        <button class="btn btn-secondary btn-small" id="cancel-edit-${noteId}">Cancel</button>
    `;

    noteElement.classList.add('edit-mode');

    // Bind events for edit buttons
    document.getElementById(`save-edit-${noteId}`).addEventListener('click', () => saveEditedNote(noteId));
    document.getElementById(`cancel-edit-${noteId}`).addEventListener('click', () => cancelEdit(noteId));

    // Focus on textarea and setup character counter
    const textarea = document.getElementById(`edit-textarea-${noteId}`);
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length, textarea.value.length);

    // Initialize character counter
    updateCharCounter(textarea, `edit-char-counter-${noteId}`, 1000);

    // Add input event listener for character counter
    textarea.addEventListener('input', function() {
        updateCharCounter(this, `edit-char-counter-${noteId}`, 1000);
    });

    editingNoteId = noteId;
}

// Save edited note
async function saveEditedNote(noteId) {
    const textarea = document.getElementById(`edit-textarea-${noteId}`);
    const newContent = textarea.value.trim();

    if (!validateInput(newContent, 'note')) {
        if (!newContent) {
            showStatus('notes-status', 'Note content cannot be empty', 'error');
        } else if (newContent.length > 1000) {
            showStatus('notes-status', 'Note content cannot exceed 1000 characters', 'error');
        } else {
            showStatus('notes-status', 'Note content must be at least 1 character', 'error');
        }
        return;
    }

    if (newContent.length > CONFIG.MAX_NOTE_LENGTH) {
        showStatus('notes-status', `Note content cannot exceed ${CONFIG.MAX_NOTE_LENGTH} characters`, 'error');
        return;
    }

    try {
        LoadingManager.showLoading('load-save');
        showStatus('notes-status', 'Updating note...', 'info');

        // Generate new preview title
        const previewLength = Math.min(Math.floor(newContent.length / 3), 15);
        const previewTitle = newContent.substring(0, previewLength);

        const encryptedContent = await CryptoUtils.encryptText(newContent, encryptionKey);
        const encryptedPreviewTitle = await CryptoUtils.encryptText(previewTitle, encryptionKey);
        const result = await apiManager.updateNote(noteId, encryptedContent, encryptedPreviewTitle);

        if (result.success) {
            // Clear decrypted content from memory
            decryptedNotes.delete(noteId);
            showStatus('notes-status', 'Note updated successfully!', 'success');
            loadNotes();
            editingNoteId = null;
        } else {
            showStatus('notes-status', result.error || 'Update failed, please try again', 'error');
        }
    } catch (error) {
        console.error('Update note error:', error);
        showStatus('notes-status', 'Failed to update note', 'error');
    } finally {
        LoadingManager.hideLoading('load-save');
    }
}

// Cancel edit
function cancelEdit(noteId) {
    // Clear decrypted content from memory
    decryptedNotes.delete(noteId);
    editingNoteId = null;
    loadNotes(); // Reload notes to restore original content
}

// Delete note
async function deleteNote(noteId) {
    if (!confirm('Are you sure you want to delete this note?')) {
        return;
    }

    try {
        LoadingManager.showLoading('load-delete');
        showStatus('notes-status', 'Deleting note...', 'info');

        const result = await apiManager.deleteNote(noteId);

        if (result.success) {
            // Clear decrypted content from memory
            decryptedNotes.delete(noteId);
            showStatus('notes-status', 'Note deleted successfully!', 'success');
            loadNotes();
        } else {
            showStatus('notes-status', result.error || 'Delete failed, please try again', 'error');
        }
    } catch (error) {
        console.error('Delete note error:', error);
        showStatus('notes-status', 'Failed to delete note', 'error');
    } finally {
        LoadingManager.hideLoading('load-delete');
    }
}

// Clear all notes
async function clearAllNotes() {
    try {
        LoadingManager.showLoading('load-delete');
        showStatus('notes-status', 'Deleting all notes...', 'info');

        const result = await apiManager.deleteAllNotes();

        if (result.success) {
            // Clear all decrypted content from memory
            decryptedNotes.clear();
            showStatus('notes-status', 'All notes deleted successfully!', 'success');
            loadNotes();
        } else {
            showStatus('notes-status', result.error || 'Failed to delete notes', 'error');
        }
    } catch (error) {
        console.error('Clear notes error:', error);
        showStatus('notes-status', 'Failed to delete notes', 'error');
    } finally {
        LoadingManager.hideLoading('load-delete');
    }
}

// Load user statistics
async function loadUserStats() {
    try {
        const stats = await apiManager.getUserStats();
        const statsEl = document.getElementById('user-stats');

        if (statsEl && stats.noteCount !== undefined) {
            statsEl.innerHTML = `<p>Total notes: ${stats.noteCount}</p>`;
        }
    } catch (error) {
        console.error('Failed to load user stats:', error);
    }
}

// Expand note to show full content
async function expandNoteContent(noteId) {
    const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
    if (!noteElement) return;

    const contentElement = noteElement.querySelector('.note-content');
    const expandBtn = noteElement.querySelector('.note-expand-btn');

    // Decrypt full content if not already decrypted
    let fullContent = decryptedNotes.get(noteId);
    if (!fullContent) {
        const note = allNotes.find(n => n.id == noteId);
        if (note) {
            try {
                fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                decryptedNotes.set(noteId, fullContent);
            } catch (error) {
                showStatus('notes-status', 'Failed to decrypt note', 'error');
                return;
            }
        }
    }

    // Update content and button
    contentElement.innerHTML = escapeHTML(fullContent);
    contentElement.classList.remove('collapsed');
    expandBtn.textContent = '收起';
    expandBtn.setAttribute('onclick', `collapseNote(${noteId})`);
}

// Collapse note to show preview only
async function collapseNoteContent(noteId) {
    const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
    if (!noteElement) return;

    const contentElement = noteElement.querySelector('.note-content');
    const expandBtn = noteElement.querySelector('.note-expand-btn');
    const note = allNotes.find(n => n.id == noteId);

    if (note && note.encrypted_preview_title) {
        try {
            const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
            contentElement.innerHTML = escapeHTML(previewTitle) + '...';
        } catch (error) {
            contentElement.innerHTML = 'Unable to decrypt preview';
        }
    } else {
        contentElement.innerHTML = 'Click to expand...';
    }

    contentElement.classList.add('collapsed');
    expandBtn.textContent = '展开';
    expandBtn.setAttribute('onclick', `expandNote(${noteId})`);

    // Clear full content from memory to save space
    decryptedNotes.delete(noteId);
}

// Search notes across all pages with scope selection
async function searchNotes(keyword) {
    const notesListEl = document.getElementById('notes-list');
    const searchScope = document.getElementById('search-scope').value;

    if (!keyword.trim()) {
        // If search is empty, clear editing state and reload current page
        editingNoteId = null;
        loadNotes(currentPage);
        return;
    }

    // Clear any existing editing state when starting search
    editingNoteId = null;

    // Show search progress in title
    const scopeText = searchScope === 'title' ? 'titles' : 'content';
    notesListEl.innerHTML = `<h4 id="notes-title">🔍 Searching ${scopeText}... (0/${allNotes.length})</h4>`;

    // Hide pagination during search
    document.getElementById('pagination-controls').style.display = 'none';

    let matchingNotes = [];
    let searchedCount = 0;
    const keywordLower = keyword.toLowerCase();

    try {
        // Search through all notes based on selected scope
        for (const note of allNotes) {
            searchedCount++;
            let isMatch = false;

            try {
                if (searchScope === 'title') {
                    // Only search in preview titles
                    if (note.encrypted_preview_title) {
                        try {
                            const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                            if (previewTitle.toLowerCase().includes(keywordLower)) {
                                isMatch = true;
                            }
                        } catch (error) {
                            // Skip preview title if can't decrypt
                        }
                    }
                } else {
                    // Search in both preview title and full content
                    // First check preview title if available
                    if (note.encrypted_preview_title) {
                        try {
                            const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                            if (previewTitle.toLowerCase().includes(keywordLower)) {
                                isMatch = true;
                            }
                        } catch (error) {
                            // Skip preview title if can't decrypt
                        }
                    }

                    // If not found in preview, search full content
                    if (!isMatch) {
                        const fullContent = await CryptoUtils.decryptText(note.encrypted_content, encryptionKey);
                        if (fullContent.toLowerCase().includes(keywordLower)) {
                            isMatch = true;
                        }
                    }
                }

                if (isMatch) {
                    matchingNotes.push(note);
                }
            } catch (error) {
                // Skip notes that can't be decrypted
                console.error('Failed to decrypt note for search:', error);
            }

            // Update progress in title
            if (searchedCount % 5 === 0 || searchedCount === allNotes.length) {
                const titleEl = document.getElementById('notes-title');
                if (titleEl) {
                    const percentage = Math.round((searchedCount / allNotes.length) * 100);
                    titleEl.textContent = `🔍 Searching ${scopeText}... (${searchedCount}/${allNotes.length} - ${percentage}% - Found: ${matchingNotes.length})`;
                }
                // Allow UI to update
                await new Promise(resolve => setTimeout(resolve, 1));
            }
        }

        // Display search results
        await displaySearchResults(matchingNotes, keyword, searchScope);

    } catch (error) {
        console.error('Search error:', error);
        notesListEl.innerHTML = `<h4>❌ Search Failed - Please try again</h4>`;
    }
}

// Display search results
async function displaySearchResults(matchingNotes, keyword, searchScope) {
    const notesListEl = document.getElementById('notes-list');
    const scopeLabel = searchScope === 'title' ? 'titles' : 'content';

    // Update title with final results
    let notesHtml = '';
    if (matchingNotes.length === 0) {
        notesHtml = `<h4>🔍 No results found in ${scopeLabel} for "${escapeHTML(keyword)}" (searched ${allNotes.length} notes)</h4>`;
    } else {
        notesHtml = `<h4>🔍 Found ${matchingNotes.length} result(s) in ${scopeLabel} for "${escapeHTML(keyword)}"</h4>`;

        // Add the matching notes
        for (const note of matchingNotes) {
            try {
                const createdAt = new Date(note.created_at).toLocaleString('zh-CN');
                let displayContent = '';
                let hasExpandBtn = false;

                // Try to decrypt preview title first
                if (note.encrypted_preview_title) {
                    try {
                        const previewTitle = await CryptoUtils.decryptText(note.encrypted_preview_title, encryptionKey);
                        displayContent = escapeHTML(previewTitle) + '...';
                        hasExpandBtn = true;
                    } catch (error) {
                        displayContent = 'Unable to decrypt preview';
                    }
                } else {
                    displayContent = 'Click to expand...';
                    hasExpandBtn = true;
                }

                notesHtml += `
                    <div class="note-item" data-note-id="${note.id}">
                        <div class="note-actions">
                            <button class="btn btn-small edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                            <button class="btn btn-danger btn-small delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
                        </div>
                        <div class="note-meta">Created: ${createdAt}</div>
                        <div class="note-content collapsed">${displayContent}</div>
                        ${hasExpandBtn ? '<button class="note-expand-btn" onclick="expandNote(' + note.id + ')">展开</button>' : ''}
                    </div>
                `;
            } catch (error) {
                console.error('Failed to process search result:', error);
            }
        }
    }

    notesListEl.innerHTML = notesHtml;
    // Hide pagination during search
    document.getElementById('pagination-controls').style.display = 'none';
}

// Event delegation for note actions
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('edit-note-btn')) {
        const noteId = e.target.getAttribute('data-note-id');
        editNote(noteId);
    } else if (e.target.classList.contains('delete-note-btn')) {
        const noteId = e.target.getAttribute('data-note-id');
        deleteNote(noteId);
    }
});

// Make functions available globally
window.expandNote = expandNoteContent;
window.collapseNote = collapseNoteContent;
