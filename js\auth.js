/**
 * Authentication module for SecureNotes application
 */

// Global state
let currentUser = null;
let encryptionKey = null;
let apiManager = null;
let accessToken = null;
let accessTokenExpiry = null;

// Initialize API manager
function initializeAPI() {
    if (!apiManager) {
        apiManager = new APIManager();
    }
}

// Site access verification
async function checkAccessRequirement() {
    try {
        const response = await secureApiRequest('/api/site-access-check');
        
        if (response.accessRequired === false) {
            document.getElementById('access-gate').style.display = 'none';
            document.getElementById('main-app').classList.add('show');
            checkExistingSession();
        } else {
            document.getElementById('access-gate').style.display = 'block';
            checkSiteAccess();
        }
    } catch (error) {
        document.getElementById('access-gate').style.display = 'block';
        checkSiteAccess();
    }
}

function checkSiteAccess() {
    try {
        if (sessionData.siteAccessVerified &&
            sessionData.accessExpiry &&
            Date.now() < sessionData.accessExpiry &&
            accessToken &&
            accessTokenExpiry &&
            Date.now() < accessTokenExpiry) {

            document.getElementById('access-gate').style.display = 'none';
            document.getElementById('main-app').classList.add('show');
            checkExistingSession();
        }
    } catch (error) {
        // Site access check failed
    }
}

async function verifySiteAccess() {
    const password = document.getElementById('site-password').value;
    
    if (!password) {
        showStatus('access-status', 'Please enter the access password', 'error');
        return;
    }

    try {
        showStatus('access-status', 'Verifying access...', 'info');
        
        const response = await secureApiRequest('/api/verify-site-access', {
            method: 'POST',
            body: JSON.stringify({ password })
        });

        if (response.success) {
            sessionData.siteAccessVerified = true;
            sessionData.accessTimestamp = Date.now();
            sessionData.accessExpiry = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
            
            accessToken = response.token;
            accessTokenExpiry = Date.now() + (24 * 60 * 60 * 1000);

            document.getElementById('access-gate').style.display = 'none';
            document.getElementById('main-app').classList.add('show');
            document.getElementById('site-password').value = '';
            
            checkExistingSession();
        } else {
            showStatus('access-status', response.error || 'Invalid access password', 'error');
        }
    } catch (error) {
        showStatus('access-status', 'Access verification failed', 'error');
    }
}

function checkExistingSession() {
    initializeAPI();
    
    if (currentUser && apiManager && apiManager.isTokenValid()) {
        showNavigationLinks();
        window.location.href = 'notes.html';
    } else {
        showAuthSection();
    }
}

// Authentication UI
function showAuthTab(tab) {
    document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.auth-section').forEach(s => s.classList.remove('active'));
    
    document.querySelector(`[onclick="showAuthTab('${tab}')"]`).classList.add('active');
    document.getElementById(`${tab}-section`).classList.add('active');
}

function showAuthSection() {
    document.getElementById('auth-container').style.display = 'block';
    document.getElementById('2fa-login-section').classList.remove('active');
    document.getElementById('login-section').classList.add('active');
}

function show2FASection() {
    document.getElementById('auth-container').style.display = 'block';
    document.querySelectorAll('.auth-section').forEach(s => s.classList.remove('active'));
    document.getElementById('2fa-login-section').classList.add('active');
}

// Password strength validation
function updatePasswordStrengthIndicator(password) {
    const strengthEl = document.getElementById('password-strength-text');
    const hintEl = document.getElementById('password-strength-hint');
    
    if (!password) {
        hintEl.style.display = 'none';
        return;
    }

    let score = 0;
    let feedback = [];

    if (password.length >= 8) score++;
    else feedback.push('At least 8 characters');

    if (/[a-z]/.test(password)) score++;
    else feedback.push('Lowercase letters');

    if (/[A-Z]/.test(password)) score++;
    else feedback.push('Uppercase letters');

    if (/[0-9]/.test(password)) score++;
    else feedback.push('Numbers');

    if (/[^A-Za-z0-9]/.test(password)) score++;
    else feedback.push('Special characters');

    hintEl.className = 'password-strength';
    if (score < 3) {
        hintEl.classList.add('weak');
        strengthEl.textContent = `Weak password. Add: ${feedback.join(', ')}`;
    } else if (score < 5) {
        hintEl.classList.add('medium');
        strengthEl.textContent = `Medium strength. Consider adding: ${feedback.join(', ')}`;
    } else {
        hintEl.classList.add('strong');
        strengthEl.textContent = 'Strong password!';
    }
    
    hintEl.style.display = 'block';
}

// Authentication functions
async function register() {
    const email = document.getElementById('register-email').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;

    if (!validateInput(email, 'email')) {
        showStatus('auth-status', 'Please enter a valid email address', 'error');
        return;
    }

    if (!validateInput(password, 'password')) {
        showStatus('auth-status', 'Password must be at least 8 characters long', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showStatus('auth-status', 'Passwords do not match', 'error');
        return;
    }

    try {
        LoadingManager.showLoading('load-auth');
        showStatus('auth-status', 'Creating account...', 'info');

        initializeAPI();

        const baseSalt = await CryptoUtils.generateSalt();
        const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
        const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);

        const result = await apiManager.register(email, authKey, baseSalt);

        if (result.success) {
            showStatus('auth-status', 'Account created successfully! Please login.', 'success');
            
            // Clear form and switch to login
            document.getElementById('register-email').value = '';
            document.getElementById('register-password').value = '';
            document.getElementById('confirm-password').value = '';
            
            setTimeout(() => {
                showAuthTab('login');
                document.getElementById('login-email').value = email;
            }, 2000);
        } else {
            showStatus('auth-status', result.error || 'Registration failed', 'error');
        }
    } catch (error) {
        showStatus('auth-status', 'Registration failed. Please try again.', 'error');
    } finally {
        LoadingManager.hideLoading('load-auth');
    }
}

async function login() {
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    if (!validateInput(email, 'email')) {
        showStatus('auth-status', 'Please enter a valid email address', 'error');
        return;
    }

    if (!password) {
        showStatus('auth-status', 'Please enter your password', 'error');
        return;
    }

    try {
        LoadingManager.showLoading('load-auth');
        showStatus('auth-status', 'Logging in...', 'info');

        initializeAPI();

        const baseSalt = await CryptoUtils.generateSalt();
        const authSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'auth');
        const authKey = await CryptoUtils.deriveAuthKey(password, authSalt);

        const result = await apiManager.login(email, authKey);

        if (result.success) {
            if (result.requires2FA) {
                apiManager.setSecureToken(result.tempToken);
                show2FASection();
                showStatus('auth-status', '', 'info');
            } else {
                await completeLogin(result, email, password, baseSalt);
            }
        } else {
            showStatus('auth-status', result.error || 'Login failed', 'error');
        }
    } catch (error) {
        showStatus('auth-status', 'Login failed. Please try again.', 'error');
    } finally {
        LoadingManager.hideLoading('load-auth');
    }
}

async function verify2FALogin() {
    const totpCode = document.getElementById('2fa-login-code').value;
    const backupCode = document.getElementById('2fa-backup-code-login').value;

    if (!totpCode && !backupCode) {
        showStatus('auth-status', 'Please enter a verification code or backup code', 'error');
        return;
    }

    try {
        LoadingManager.showLoading('load-auth');
        showStatus('auth-status', 'Verifying...', 'info');

        const result = await apiManager.verify2FA(apiManager.token, totpCode, backupCode);

        if (result.success) {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const baseSalt = await CryptoUtils.generateSalt();
            
            await completeLogin(result, email, password, baseSalt);
        } else {
            showStatus('auth-status', result.error || '2FA verification failed', 'error');
        }
    } catch (error) {
        showStatus('auth-status', '2FA verification failed. Please try again.', 'error');
    } finally {
        LoadingManager.hideLoading('load-auth');
    }
}

async function completeLogin(result, email, password, baseSalt) {
    apiManager.setSecureToken(result.token);
    
    const encryptionSalt = await CryptoUtils.deriveSubSalt(baseSalt, 'encryption');
    encryptionKey = await CryptoUtils.deriveKey(password, encryptionSalt);
    currentUser = email;

    showStatus('auth-status', 'Login successful!', 'success');
    
    // Clear password for security
    document.getElementById('login-password').value = '';
    
    showNavigationLinks();
    
    setTimeout(() => {
        window.location.href = 'notes.html';
    }, 1000);
}

function showNavigationLinks() {
    document.getElementById('notes-link').style.display = 'inline-block';
    document.getElementById('settings-link').style.display = 'inline-block';
    
    // Check admin status
    if (apiManager) {
        apiManager.checkAdminStatus().then(result => {
            if (result.isAdmin) {
                document.getElementById('admin-link').style.display = 'inline-block';
            }
        });
    }
}

function logout() {
    currentUser = null;
    encryptionKey = null;
    
    if (apiManager) {
        apiManager.clearToken();
    }
    
    sessionData.siteAccessVerified = false;
    sessionData.accessTimestamp = null;
    sessionData.accessExpiry = null;
    accessToken = null;
    accessTokenExpiry = null;
    
    window.location.href = 'index.html';
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Password strength indicator
    const registerPassword = document.getElementById('register-password');
    if (registerPassword) {
        registerPassword.addEventListener('input', function(e) {
            const hintElement = document.getElementById('password-strength-hint');
            if (e.target.value.length > 0) {
                hintElement.style.display = 'block';
                updatePasswordStrengthIndicator(e.target.value);
            } else {
                hintElement.style.display = 'none';
            }
        });
    }

    // Enter key handlers
    const sitePassword = document.getElementById('site-password');
    if (sitePassword) {
        sitePassword.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                verifySiteAccess();
            }
        });
    }

    const loginEmail = document.getElementById('login-email');
    if (loginEmail) {
        loginEmail.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('login-password').focus();
            }
        });
    }

    const loginPassword = document.getElementById('login-password');
    if (loginPassword) {
        loginPassword.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                login();
            }
        });
    }
});
