/* Notes page specific styles */
.note-controls {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.search-section {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.search-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-container {
    position: relative;
    display: inline-block;
    margin-left: 15px;
}

.search-container input {
    width: 200px;
    padding: 8px 30px 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-container input:focus {
    outline: none;
    border-color: #667eea;
}

.search-clear-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
}

.search-clear-btn:hover {
    color: #666;
}

.search-container input:not(:placeholder-shown) + .search-clear-btn {
    display: flex;
}

.search-scope-select {
    padding: 6px 8px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 12px;
    background: white;
    cursor: pointer;
    min-width: 90px;
}

.search-scope-select:focus {
    outline: none;
    border-color: #667eea;
}

.note-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-left: 4px solid #667eea;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    position: relative;
    transition: all 0.3s;
}

.note-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.note-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s;
}

.note-item:hover .note-actions {
    opacity: 1;
}

.note-meta {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 8px;
}

.note-content {
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
    margin-right: 80px;
}

.note-item.edit-mode .note-content {
    margin-right: 0;
}

.note-content.collapsed {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.note-expand-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-top: 8px;
    transition: all 0.3s;
}

.note-expand-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.note-item.edit-mode {
    background: #fff3cd;
    border-left-color: #ffc107;
    padding: 12px 16px;
}

.note-item.edit-mode .note-actions {
    margin-bottom: 8px;
}

.edit-textarea {
    width: 100%;
    min-height: 150px;
    max-height: 300px;
    padding: 12px;
    border: 2px solid #ffc107;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    margin-top: 8px;
    margin-right: 0;
    box-sizing: border-box;
}

.edit-actions {
    display: flex;
    gap: 10px;
    margin-top: 8px;
    justify-content: flex-end;
    align-items: center;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-btn:hover:not(:disabled) {
    background: #e9ecef;
    color: #495057;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .note-actions {
        position: static;
        margin-top: 10px;
        justify-content: flex-end;
    }

    .note-controls {
        flex-direction: column;
        gap: 10px;
    }

    .search-section {
        margin-left: 0;
        align-self: stretch;
        flex-direction: column;
        align-items: stretch;
    }

    .search-controls {
        flex-direction: column;
        gap: 8px;
    }

    .search-container {
        margin-left: 0;
    }

    .search-container input {
        width: 100%;
    }

    .search-scope-select {
        width: 100%;
        min-width: auto;
    }

    .note-content {
        margin-right: 0;
    }

    .note-item {
        padding: 12px;
    }

    .pagination-controls {
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination-btn {
        padding: 6px 10px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .note-controls {
        flex-direction: column;
        gap: 15px;
    }
}
