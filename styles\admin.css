/* Admin page specific styles */
.admin-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #dc3545;
}

.admin-section h3 {
    color: #dc3545;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.admin-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.user-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s;
}

.user-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-item.user-disabled {
    background: #f8f9fa;
    border-color: #dee2e6;
    opacity: 0.7;
}

.user-info {
    flex: 1;
}

.user-email {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.user-meta {
    font-size: 12px;
    color: #6c757d;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.user-status.active {
    background: #d4edda;
    color: #155724;
}

.user-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

.toggle-user-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.toggle-user-btn.enable {
    background: #28a745;
    color: white;
}

.toggle-user-btn.disable {
    background: #dc3545;
    color: white;
}

.toggle-user-btn:hover {
    opacity: 0.8;
}

.toggle-user-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.backup-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.backup-status {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.backup-status h4 {
    color: #333;
    margin-bottom: 10px;
}

.backup-status p {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
}

.backup-files {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    background: white;
}

.backup-file {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.backup-file:last-child {
    border-bottom: none;
}

.backup-file:hover {
    background: #f8f9fa;
}

.backup-file-info {
    flex: 1;
}

.backup-file-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.backup-file-meta {
    font-size: 12px;
    color: #6c757d;
}

.backup-file-actions {
    display: flex;
    gap: 8px;
}

.backup-file-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 8px;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .admin-controls {
        flex-direction: column;
    }

    .user-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .user-actions {
        justify-content: space-between;
    }

    .backup-controls {
        flex-direction: column;
    }

    .backup-file {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .backup-file-actions {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .admin-section {
        padding: 15px;
    }

    .user-item {
        padding: 12px;
    }

    .backup-file {
        padding: 10px 12px;
    }
}
