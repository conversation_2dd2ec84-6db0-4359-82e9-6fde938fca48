/**
 * Cryptographic utilities for SecureNotes application
 * Implements AES-256-GCM encryption with PBKDF2 key derivation
 */

class CryptoUtils {
    // Security: Enhanced key validation
    static async validateKeyStrength(key) {
        try {
            if (!key) return false;
            
            // Check if key is a CryptoKey object
            if (key.constructor.name !== 'CryptoKey') return false;
            
            // Verify key algorithm and usage
            if (key.algorithm.name !== 'AES-GCM') return false;
            if (!key.usages.includes('encrypt') || !key.usages.includes('decrypt')) return false;
            
            return true;
        } catch (error) {
            return false;
        }
    }

    // Security: Clear sensitive data from memory
    static clearSensitiveData(data) {
        if (data && data.fill) {
            data.fill(0);
        }
    }

    static async generateSalt() {
        return window.crypto.getRandomValues(new Uint8Array(32));
    }

    static async deriveSubSalt(baseSalt, purpose) {
        const encoder = new TextEncoder();
        const purposeBytes = encoder.encode(purpose);
        const combined = new Uint8Array(baseSalt.length + purposeBytes.length);
        combined.set(baseSalt);
        combined.set(purposeBytes, baseSalt.length);
        
        const hash = await window.crypto.subtle.digest('SHA-256', combined);
        return new Uint8Array(hash);
    }

    static async deriveKey(password, salt) {
        const enc = new TextEncoder();
        const keyMaterial = await window.crypto.subtle.importKey(
            'raw',
            enc.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        const key = await window.crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 100000,
                hash: 'SHA-256',
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['encrypt', 'decrypt']
        );

        return key;
    }

    static async deriveAuthKey(password, salt) {
        const enc = new TextEncoder();
        const keyMaterial = await window.crypto.subtle.importKey(
            'raw',
            enc.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        const authKey = await window.crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 300000, // Higher iterations for auth key security
                hash: 'SHA-256',
            },
            keyMaterial,
            { name: 'HMAC', hash: 'SHA-256' },
            true,
            ['sign']
        );

        const exported = await window.crypto.subtle.exportKey('raw', authKey);
        return btoa(String.fromCharCode(...new Uint8Array(exported)));
    }

    static async encryptText(plainText, key) {
        try {
            // Validate inputs
            if (!plainText || typeof plainText !== 'string') {
                throw new Error('Invalid plaintext data');
            }
            if (!key) {
                throw new Error('Invalid encryption key');
            }

            // Validate key before use
            if (!(await this.validateKeyStrength(key))) {
                throw new Error('Encryption key failed validation');
            }

            const enc = new TextEncoder();
            const encodedText = enc.encode(plainText);

            // Generate cryptographically secure random IV
            const iv = window.crypto.getRandomValues(new Uint8Array(12));

            const encrypted = await window.crypto.subtle.encrypt(
                { name: 'AES-GCM', iv: iv },
                key,
                encodedText
            );

            const combined = new Uint8Array(iv.length + encrypted.byteLength);
            combined.set(iv);
            combined.set(new Uint8Array(encrypted), iv.length);

            const result = btoa(String.fromCharCode(...combined));

            // Clear sensitive data
            this.clearSensitiveData(encodedText);
            this.clearSensitiveData(iv);

            return result;
        } catch (error) {
            throw new Error('Encryption failed');
        }
    }

    static async decryptText(encryptedData, key) {
        try {
            // Validate inputs
            if (!encryptedData || typeof encryptedData !== 'string') {
                throw new Error('Invalid encrypted data');
            }
            if (!key) {
                throw new Error('Invalid decryption key');
            }

            // Validate key before use
            if (!(await this.validateKeyStrength(key))) {
                throw new Error('Decryption key failed validation');
            }

            const combined = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
            
            if (combined.length < 12) {
                throw new Error('Invalid encrypted data format');
            }

            const iv = combined.slice(0, 12);
            const encrypted = combined.slice(12);

            const decrypted = await window.crypto.subtle.decrypt(
                { name: 'AES-GCM', iv: iv },
                key,
                encrypted
            );

            const result = new TextDecoder().decode(decrypted);

            // Clear sensitive data
            this.clearSensitiveData(iv);
            this.clearSensitiveData(encrypted);

            return result;
        } catch (error) {
            throw new Error('Decryption failed');
        }
    }

    // Security: Secure random string generation
    static generateSecureRandom(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        const array = new Uint8Array(length);
        window.crypto.getRandomValues(array);
        return Array.from(array, byte => chars[byte % chars.length]).join('');
    }

    // Security: Key derivation for different purposes
    static async deriveKeyForPurpose(password, salt, purpose) {
        const purposeSalt = await this.deriveSubSalt(salt, purpose);
        return await this.deriveKey(password, purposeSalt);
    }

    // Security: Secure key comparison
    static async compareKeys(key1, key2) {
        try {
            if (!key1 || !key2) return false;
            
            const exported1 = await window.crypto.subtle.exportKey('raw', key1);
            const exported2 = await window.crypto.subtle.exportKey('raw', key2);
            
            const array1 = new Uint8Array(exported1);
            const array2 = new Uint8Array(exported2);
            
            if (array1.length !== array2.length) return false;
            
            let result = 0;
            for (let i = 0; i < array1.length; i++) {
                result |= array1[i] ^ array2[i];
            }
            
            // Clear sensitive data
            this.clearSensitiveData(array1);
            this.clearSensitiveData(array2);
            
            return result === 0;
        } catch (error) {
            return false;
        }
    }

    // Security: Memory cleanup for keys
    static async clearKey(key) {
        try {
            if (key && typeof key.algorithm !== 'undefined') {
                // Key is already a CryptoKey, cannot be directly cleared
                // Browser will handle garbage collection
                return true;
            }
        } catch (error) {
            // Key cleanup failed silently
        }
        return false;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CryptoUtils };
}

// Make available globally for browser usage
if (typeof window !== 'undefined') {
    window.CryptoUtils = CryptoUtils;
}
