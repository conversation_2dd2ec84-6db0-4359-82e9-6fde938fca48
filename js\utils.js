/**
 * Common utility functions for SecureNotes application
 */

// Security: Configuration constants with validation
const CONFIG = {
    API_BASE: 'https://secure-notes-api.v8x.workers.dev',
    ALLOWED_DOMAINS: ['secure-notes-api.v8x.workers.dev'],
    SITE_ACCESS_ENABLED: true,
    MAX_NOTE_LENGTH: 10000,
    SESSION_TIMEOUT: 60 * 60 * 1000, // 1 hour
    AUTO_LOGOUT_INACTIVE: 3 * 60 * 1000, // 3 minutes
    REQUEST_TIMEOUT: 30000, // 30 seconds
    MAX_RETRY_ATTEMPTS: 3,
    EXPECTED_DOMAIN_HASH: '8b0e7c9c4b5f6a2d1e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6'
};

// Security: Verify configuration integrity
async function verifyConfigIntegrity() {
    try {
        const domainStr = CONFIG.ALLOWED_DOMAINS.join(',');
        const hash = await sha256(domainStr);
        const expectedHash = CONFIG.EXPECTED_DOMAIN_HASH;

        if (hash !== expectedHash) {
            // Configuration mismatch detected
            console.warn('Configuration integrity check failed');
        }
    } catch (error) {
        console.warn('Configuration integrity check error:', error);
    }
}

// Security: Validate API endpoint
function validateApiEndpoint(url) {
    try {
        const urlObj = new URL(url);
        return CONFIG.ALLOWED_DOMAINS.includes(urlObj.hostname);
    } catch {
        return false;
    }
}

// Security: Input validation functions
function validateInput(input, type, maxLength = 1000) {
    if (!input || typeof input !== 'string') return false;
    if (input.length > maxLength) return false;

    switch (type) {
        case 'email':
            return /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(input);
        case 'password':
            return input.length >= 8;
        case 'noteId':
            return /^\d+$/.test(input);
        default:
            return true;
    }
}

// Security: HTML escape function to prevent XSS attacks
function escapeHTML(str) {
    if (typeof str !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

// Security: Secure hash function
async function sha256(message) {
    const msgBuffer = new TextEncoder().encode(message);
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Security: Secure API request function with validation
async function secureApiRequest(endpoint, options = {}) {
    const url = `${CONFIG.API_BASE}${endpoint}`;

    // Validate API endpoint
    if (!validateApiEndpoint(url)) {
        throw new Error('Invalid API endpoint');
    }

    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };

    const requestOptions = {
        method: 'GET',
        headers,
        ...options
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

    try {
        requestOptions.signal = controller.signal;
        const response = await fetch(url, requestOptions);
        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
            throw new Error('Request timeout');
        }
        throw error;
    }
}

// UI utility functions
function showStatus(elementId, message, type = 'info') {
    const statusEl = document.getElementById(elementId);
    if (!statusEl) return;

    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = message ? 'block' : 'none';

    if (message && type !== 'info') {
        setTimeout(() => {
            statusEl.style.display = 'none';
        }, 5000);
    }
}

function showLoading(elementId, show = true) {
    const loadingEl = document.getElementById(elementId);
    if (!loadingEl) return;

    if (show) {
        loadingEl.classList.add('show');
    } else {
        loadingEl.classList.remove('show');
    }
}

// Character counter utility
function updateCharCounter(input, counterId, maxLength) {
    const counter = document.getElementById(counterId);
    if (!counter) return;

    const currentLength = input.value.length;
    counter.textContent = `${currentLength}/${maxLength}`;

    counter.classList.remove('warning', 'error');
    if (currentLength > maxLength * 0.9) {
        counter.classList.add('error');
    } else if (currentLength > maxLength * 0.8) {
        counter.classList.add('warning');
    }
}

// Memory-only storage implementation
class MemoryOnlyStorage {
    constructor() {
        this.data = new Map();
    }

    setItem(key, value) {
        this.data.set(key, value);
    }

    getItem(key) {
        return this.data.get(key) || null;
    }

    removeItem(key) {
        this.data.delete(key);
    }

    clear() {
        this.data.clear();
    }

    key(index) {
        const keys = Array.from(this.data.keys());
        return keys[index] || null;
    }

    get length() {
        return this.data.size;
    }
}

// Loading manager
class LoadingManager {
    static activeLoaders = new Set();

    static showLoading(id) {
        this.activeLoaders.add(id);
        showLoading(id, true);
    }

    static hideLoading(id) {
        this.activeLoaders.delete(id);
        showLoading(id, false);
    }

    static hideAllLoading() {
        this.activeLoaders.forEach(id => {
            showLoading(id, false);
        });
        this.activeLoaders.clear();
    }
}

// Session management
let sessionData = {
    siteAccessVerified: false,
    accessTimestamp: null,
    accessExpiry: null,
    lastActivity: Date.now()
};

// Auto-lock configuration
const AUTO_LOCK_TIMEOUT = 3 * 60 * 1000; // 3 minutes
let autoLockTimer = null;

// Activity tracking for auto-logout
function resetAutoLogoutTimer() {
    sessionData.lastActivity = Date.now();
    
    if (autoLockTimer) {
        clearTimeout(autoLockTimer);
    }
    
    autoLockTimer = setTimeout(() => {
        if (typeof logout === 'function') {
            logout();
        }
    }, AUTO_LOCK_TIMEOUT);
}

// Initialize activity listeners
function initializeActivityTracking() {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    activityEvents.forEach(event => {
        document.addEventListener(event, resetAutoLogoutTimer, true);
    });
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        verifyConfigIntegrity,
        validateApiEndpoint,
        validateInput,
        escapeHTML,
        sha256,
        secureApiRequest,
        showStatus,
        showLoading,
        updateCharCounter,
        MemoryOnlyStorage,
        LoadingManager,
        sessionData,
        resetAutoLogoutTimer,
        initializeActivityTracking
    };
}
