/**
 * API Manager for SecureNotes application
 * Handles all API communications with security validation
 */

class APIManager {
    constructor() {
        this.apiBase = CONFIG.API_BASE;
        this.token = this.getSecureToken();
    }

    // Security: Memory-only token storage
    getSecureToken() {
        return null; // Always return null - no persistent token storage
    }

    setSecureToken(token) {
        this.token = token;
        this.tokenExpiry = Date.now() + CONFIG.SESSION_TIMEOUT;
    }

    clearToken() {
        this.token = null;
        this.tokenExpiry = null;
    }

    isTokenValid() {
        return this.token && this.tokenExpiry && Date.now() < this.tokenExpiry;
    }

    async makeRequest(endpoint, options = {}) {
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return await secureApiRequest(endpoint, {
            ...options,
            headers
        });
    }

    // Authentication methods
    async register(email, authKey, salt) {
        try {
            const response = await this.makeRequest('/api/register', {
                method: 'POST',
                body: JSON.stringify({
                    email,
                    authKey,
                    salt: JSON.stringify(Array.from(salt))
                })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async login(email, authKey) {
        try {
            const response = await this.makeRequest('/api/login', {
                method: 'POST',
                body: JSON.stringify({ email, authKey })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async verify2FA(tempToken, totpCode, backupCode) {
        try {
            const response = await this.makeRequest('/api/2fa/verify-login', {
                method: 'POST',
                body: JSON.stringify({
                    tempToken,
                    totpCode,
                    backupCode
                })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Notes methods
    async getNotes() {
        try {
            const response = await this.makeRequest('/api/notes');
            return response.notes || [];
        } catch (error) {
            throw new Error('Failed to load notes');
        }
    }

    async saveNote(encryptedContent, encryptedPreviewTitle) {
        try {
            const response = await this.makeRequest('/api/notes', {
                method: 'POST',
                body: JSON.stringify({
                    encryptedContent,
                    encryptedPreviewTitle
                })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async updateNote(noteId, encryptedContent, encryptedPreviewTitle) {
        try {
            const response = await this.makeRequest(`/api/notes/${noteId}`, {
                method: 'PUT',
                body: JSON.stringify({
                    encryptedContent,
                    encryptedPreviewTitle
                })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async deleteNote(noteId) {
        try {
            const response = await this.makeRequest(`/api/notes/${noteId}`, {
                method: 'DELETE'
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async deleteAllNotes() {
        try {
            const response = await this.makeRequest('/api/notes/clear', {
                method: 'DELETE'
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // User management methods
    async getUserStats() {
        try {
            const response = await this.makeRequest('/api/stats');
            return response.stats || {};
        } catch (error) {
            return {};
        }
    }

    async changePassword(currentPassword, newPassword, baseSalt) {
        try {
            const response = await this.makeRequest('/api/change-password', {
                method: 'POST',
                body: JSON.stringify({
                    currentPassword,
                    newPassword,
                    baseSalt: JSON.stringify(Array.from(baseSalt))
                })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 2FA methods
    async get2FAStatus() {
        try {
            const response = await this.makeRequest('/api/2fa/status');
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async setup2FA() {
        try {
            const response = await this.makeRequest('/api/2fa/setup', {
                method: 'POST'
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async verify2FASetup(token) {
        try {
            const response = await this.makeRequest('/api/2fa/verify', {
                method: 'POST',
                body: JSON.stringify({ token })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async disable2FA(token) {
        try {
            const response = await this.makeRequest('/api/2fa/disable', {
                method: 'POST',
                body: JSON.stringify({ token })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async generate2FABackupCodes(token) {
        try {
            const response = await this.makeRequest('/api/2fa/backup-codes', {
                method: 'POST',
                body: JSON.stringify({ token })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Admin methods
    async checkAdminStatus() {
        try {
            const response = await this.makeRequest('/api/admin/check');
            return response;
        } catch (error) {
            return { success: false, isAdmin: false };
        }
    }

    async getUsers() {
        try {
            const response = await this.makeRequest('/api/admin/users');
            return response.users || [];
        } catch (error) {
            return [];
        }
    }

    async toggleUserStatus(userId, disable) {
        try {
            const response = await this.makeRequest(`/api/admin/users/${userId}/toggle`, {
                method: 'POST',
                body: JSON.stringify({ disable })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async createBackup() {
        try {
            const response = await this.makeRequest('/api/admin/backup', {
                method: 'POST'
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async getBackupFiles() {
        try {
            const response = await this.makeRequest('/api/admin/backup/files');
            return response.files || [];
        } catch (error) {
            return [];
        }
    }

    async downloadBackup(fileId) {
        try {
            const response = await this.makeRequest(`/api/admin/backup/download/${fileId}`);
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async deleteBackup(fileId) {
        try {
            const response = await this.makeRequest(`/api/admin/backup/delete/${fileId}`, {
                method: 'DELETE'
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async restoreBackup(backupData) {
        try {
            const response = await this.makeRequest('/api/admin/backup/restore', {
                method: 'POST',
                body: JSON.stringify({ backupData })
            });
            return response;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIManager };
}

// Make available globally for browser usage
if (typeof window !== 'undefined') {
    window.APIManager = APIManager;
}
